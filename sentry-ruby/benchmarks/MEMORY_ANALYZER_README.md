# Sentry Memory Usage Analyzer

This script analyzes memory usage patterns for Sentry.logger operations, focusing on memory growth visualization and Sentry-specific behavior analysis.

## Overview

The `memory_usage_comparison.rb` script has been updated to:

1. **Remove standard Logger comparison** - Focus solely on Sentry.logger behavior
2. **Add memory growth visualization** - Track and visualize memory usage over time
3. **Provide Sentry-specific insights** - Analyze buffering, flushing, and retention patterns

## Key Features

### Memory Growth Tracking
- Samples memory usage every 50 iterations during logging operations
- Tracks both allocated and retained memory over time
- Generates ASCII graphs showing memory growth curves
- Identifies memory spikes and plateaus

### Sentry-Specific Analysis
- **Memory Retention Rate**: Shows how much memory remains after garbage collection
- **Buffering Behavior**: Analyzes impact of Sentry's log event buffering
- **Object Allocation Patterns**: Tracks object creation by Sentry's logging pipeline
- **Memory Hotspots**: Identifies which logging scenarios use the most memory

### Visual Output
- Real-time progress indicators with emoji
- **Interactive HTML site** with comprehensive analysis and visualizations
- **SVG charts** with <PERSON><PERSON> gem (professional quality graphs)
- **Gnuplot integration** for advanced plotting (fallback option)
- ASCII art graphs (fallback when no graphing gems available)
- Detailed memory usage breakdown by category and log level
- Summary statistics and hotspot analysis

## Test Categories

1. **Simple Strings**: Basic log messages at different levels
2. **Structured Data**: Logging with hash attributes and metadata
3. **Exception Logging**: Error logging with exception details and backtraces
4. **Template Messages**: Parameterized log messages with substitution

## Configuration

```ruby
ITERATIONS = 1000                # Number of log messages per test
LOG_LEVELS = [:debug, :info, :warn, :error]  # Log levels to test
MEMORY_SAMPLE_INTERVAL = 50      # Sample memory every N iterations
```

## Output Files

### Interactive HTML Report
- `tmp/sentry_memory_analysis.html` - **Complete interactive web report**
- `tmp/sentry_memory_analysis.css` - Stylesheet for the HTML report

### Data Files
- `tmp/sentry_memory_analysis.json` - Detailed JSON report with all measurements
- `tmp/sentry_memory_growth.csv` - CSV file with memory growth data for analysis

### Visualization Files (when graphing gems are available)
- `tmp/memory_growth_overview.svg` - Combined overview of all categories
- `tmp/memory_growth_[category].svg` - Charts for each test category
- `tmp/memory_growth_[category]_[level].svg` - Individual level charts

## Prerequisites

### Required Gems
```bash
gem install memory_profiler benchmark-memory
```

### Optional Visualization Gems
```bash
# For high-quality SVG charts (recommended)
gem install gruff  # Requires ImageMagick

# Alternative plotting library
gem install gnuplot  # Requires gnuplot system package
```

### System Dependencies
- **ImageMagick** (for gruff): `brew install imagemagick` or `apt-get install imagemagick`
- **Gnuplot** (for gnuplot gem): `brew install gnuplot` or `apt-get install gnuplot`

## Usage

```bash
cd sentry-ruby
ruby benchmarks/memory_usage_comparison.rb

# Open the interactive HTML report
open tmp/sentry_memory_analysis.html
```

## Sample Output

```
🔍 Sentry.logger Memory Usage Analysis
======================================================================
Iterations per test: 1000
Log levels tested: debug, info, warn, error
Memory sampling interval: every 50 iterations

📊 Measuring baseline memory usage...
   Baseline: 7.58 KB allocated, 80.0 B retained

🔤 Testing simple string messages...
   Testing debug level...
     Simple debug         | Allocated:   720 KB | Retained:   208 KB | Objects:   7334

📊 Generating memory growth visualizations...

SIMPLE STRINGS - Memory Growth Over Time
============================================================

DEBUG Level:
     Memory Usage (MB) over 1000 iterations (sampled every 50):
     Max: 23.36 MB
     ████████████████████████████████████████████████████▄▄▄▄▄▄▄▄ 23.36
     [ASCII graph showing memory growth over time]
     Growth rate: 0.5% (23.23 → 23.36 MB)

📈 Sentry Memory Usage Summary
======================================================================

OVERALL SENTRY MEMORY USAGE:
  Total allocated: 12.2 MB
  Total retained:  3.97 MB
  Total objects:   126481
  Average per iteration: 125 KB
  Memory retention rate: 32.6%
  Average object size: 100.9 bytes

MEMORY HOTSPOTS:
  1. template_messages_debug: 985 KB (7.9%)
  2. template_messages_error: 984 KB (7.9%)
  [...]
```

## Key Insights

### Memory Growth Patterns
- **Linear Growth**: Most scenarios show steady memory growth during logging
- **Plateau Effects**: Memory usage stabilizes after initial allocation spikes
- **Buffering Impact**: Memory spikes occur when log buffers are flushed

### Sentry-Specific Behavior
- **High Retention Rate**: ~30% of allocated memory is retained after GC
- **Object-Heavy**: Creates 100+ objects per log entry on average
- **Template Overhead**: Template messages have highest memory overhead
- **Structured Data Cost**: Hash attributes significantly increase memory usage

### Performance Implications
- Template messages use ~40% more memory than simple strings
- Structured data logging uses ~20% more memory than simple strings
- Exception logging has moderate overhead due to backtrace processing
- Memory usage scales linearly with log volume

## Recommendations

1. **Monitor Template Usage**: Template messages have the highest memory overhead
2. **Limit Structured Data**: Use structured attributes judiciously in high-volume scenarios
3. **Consider Log Levels**: Debug level logging shows slightly higher memory usage
4. **Buffer Management**: Understand that Sentry retains significant memory for buffering

## Technical Details

### Memory Measurement
- Uses `memory_profiler` gem when available
- Falls back to GC stats for basic environments
- Samples memory at regular intervals during test execution
- Tracks both process memory (RSS) and Ruby object allocation

### Graph Generation
- ASCII art graphs show memory usage over time
- X-axis represents iterations, Y-axis represents memory in MB
- Growth rate calculations show percentage increase from start to finish
- Handles both absolute memory values and relative changes

### Data Export
- JSON report includes detailed breakdown by test category
- CSV export enables further analysis in spreadsheet applications
- Growth data includes timestamps for temporal analysis
