#!/usr/bin/env ruby
# frozen_string_literal: true

require 'logger'
require 'json'
require 'ostruct'

# Try to require optional gems for enhanced memory profiling and visualization
begin
  require 'memory_profiler'
  MEMORY_PROFILER_AVAILABLE = true
rescue LoadError
  MEMORY_PROFILER_AVAILABLE = false
  puts "Warning: memory_profiler gem not available. Using basic memory measurement."
end

begin
  require 'benchmark/memory'
  BENCHMARK_MEMORY_AVAILABLE = true
rescue LoadError
  BENCHMARK_MEMORY_AVAILABLE = false
  puts "Warning: benchmark-memory gem not available. Using basic memory measurement."
end

# Try to require graphing gems for SVG visualization
begin
  require 'gruff'
  GRUFF_AVAILABLE = true
rescue LoadError
  GRUFF_AVAILABLE = false
  puts "Info: gruff gem not available. Install with 'gem install gruff' for SVG charts."
end

begin
  require 'gnuplot'
  GNUPLOT_AVAILABLE = true
rescue LoadError
  GNUPLOT_AVAILABLE = false
  puts "Info: gnuplot gem not available. Install with 'gem install gnuplot' for advanced plots."
end

# Add the lib directory to the load path to require sentry-ruby
$LOAD_PATH.unshift(File.expand_path('../lib', __dir__))

require 'sentry-ruby'

class SentryMemoryAnalyzer
  ITERATIONS = 1000
  LOG_LEVELS = [:debug, :info, :warn, :error].freeze
  MEMORY_SAMPLE_INTERVAL = 50  # Sample memory every N iterations

  def initialize
    @results = {}
    @baseline_memory = nil
    @memory_growth_data = {}
    setup_sentry
  end

  def run
    puts "🔍 Sentry.logger Memory Usage Analysis"
    puts "=" * 70
    puts "Iterations per test: #{ITERATIONS}"
    puts "Log levels tested: #{LOG_LEVELS.join(', ')}"
    puts "Memory sampling interval: every #{MEMORY_SAMPLE_INTERVAL} iterations"
    puts

    measure_baseline
    test_simple_strings
    test_structured_data
    test_exceptions
    test_template_messages

    generate_memory_graphs
    display_summary
    generate_report
  end

  private

  def setup_sentry
    # Initialize Sentry with logging enabled
    Sentry.init do |config|
      config.dsn = "https://<EMAIL>/dummy"
      config.enable_logs = true
      config.sdk_logger = Logger.new(File::NULL) # Suppress SDK logs
      config.background_worker_threads = 0 # Synchronous for accurate measurement
      config.transport.transport_class = Sentry::DummyTransport
      config.max_log_events = 2000 # Increase buffer to avoid auto-flushing during tests
    end
  end

  def measure_baseline
    puts "📊 Measuring baseline memory usage..."

    @baseline_memory, _ = measure_memory_with_growth("baseline") do |sample_callback|
      # Just allocate some basic objects to establish baseline
      100.times { |i| "baseline_#{i}" }
    end

    puts "   Baseline: #{format_memory(@baseline_memory[:total_allocated])} allocated, " \
         "#{format_memory(@baseline_memory[:total_retained])} retained"
    puts
  end

  def test_simple_strings
    puts "🔤 Testing simple string messages..."

    LOG_LEVELS.each do |level|
      puts "   Testing #{level} level..."

      # Test Sentry logger with memory growth tracking
      sentry_memory, growth_data = measure_memory_with_growth("sentry_simple_#{level}") do |sample_callback|
        ITERATIONS.times do |i|
          Sentry.logger.send(level, "Simple log message #{i}")
          sample_callback.call(i) if (i + 1) % MEMORY_SAMPLE_INTERVAL == 0
        end
        Sentry.get_current_client.flush # Ensure all events are processed
      end

      store_results("simple_strings", level, sentry_memory)
      store_growth_data("simple_strings", level, growth_data)
      display_sentry_results("Simple #{level}", sentry_memory)
    end
    puts
  end

  def test_structured_data
    puts "🏗️  Testing structured data messages..."

    LOG_LEVELS.each do |level|
      puts "   Testing #{level} level..."

      # Test Sentry logger with structured data and memory growth tracking
      sentry_memory, growth_data = measure_memory_with_growth("sentry_structured_#{level}") do |sample_callback|
        ITERATIONS.times do |i|
          Sentry.logger.send(level, "User action performed", {
            user_id: i,
            action: "login",
            ip_address: "192.168.1.#{i % 255}",
            timestamp: Time.now.to_f,
            metadata: { browser: "Chrome", version: "91.0" }
          })
          sample_callback.call(i) if (i + 1) % MEMORY_SAMPLE_INTERVAL == 0
        end
        Sentry.get_current_client.flush
      end

      store_results("structured_data", level, sentry_memory)
      store_growth_data("structured_data", level, growth_data)
      display_sentry_results("Structured #{level}", sentry_memory)
    end
    puts
  end

  def test_exceptions
    puts "💥 Testing exception logging..."

    # Create a sample exception
    sample_exception = begin
      raise StandardError, "Sample error for testing"
    rescue => e
      e
    end

    LOG_LEVELS.each do |level|
      next if level == :debug # Skip debug for exceptions
      puts "   Testing #{level} level..."

      # Test Sentry logger with exceptions and memory growth tracking
      sentry_memory, growth_data = measure_memory_with_growth("sentry_exception_#{level}") do |sample_callback|
        ITERATIONS.times do |i|
          Sentry.logger.send(level, "Exception occurred: #{sample_exception.message}", {
            exception_class: sample_exception.class.name,
            backtrace: sample_exception.backtrace&.first(5),
            error_id: "error_#{i}"
          })
          sample_callback.call(i) if (i + 1) % MEMORY_SAMPLE_INTERVAL == 0
        end
        Sentry.get_current_client.flush
      end

      store_results("exceptions", level, sentry_memory)
      store_growth_data("exceptions", level, growth_data)
      display_sentry_results("Exception #{level}", sentry_memory)
    end
    puts
  end

  def test_template_messages
    puts "📝 Testing template messages..."

    LOG_LEVELS.each do |level|
      puts "   Testing #{level} level..."

      # Test Sentry logger with template messages and memory growth tracking
      sentry_memory, growth_data = measure_memory_with_growth("sentry_template_#{level}") do |sample_callback|
        ITERATIONS.times do |i|
          Sentry.logger.send(level, "User %{name} performed %{action} at %{time}", {
            name: "user_#{i}",
            action: "login",
            time: Time.now.strftime("%H:%M:%S")
          })
          sample_callback.call(i) if (i + 1) % MEMORY_SAMPLE_INTERVAL == 0
        end
        Sentry.get_current_client.flush
      end

      store_results("template_messages", level, sentry_memory)
      store_growth_data("template_messages", level, growth_data)
      display_sentry_results("Template #{level}", sentry_memory)
    end
    puts
  end

  def measure_memory_with_growth(label)
    # Force garbage collection before measurement
    GC.start
    GC.compact if GC.respond_to?(:compact)

    growth_data = []
    start_time = Time.now

    if MEMORY_PROFILER_AVAILABLE
      report = MemoryProfiler.report do
        yield ->(iteration) {
          # Sample memory at this iteration
          current_stats = GC.stat
          current_memory = get_memory_usage
          elapsed_time = Time.now - start_time

          growth_data << {
            iteration: iteration + 1,
            timestamp: elapsed_time,
            allocated_objects: current_stats[:total_allocated_objects],
            heap_live_slots: current_stats[:heap_live_slots],
            memory_kb: current_memory
          }
        }
      end

      memory_result = {
        label: label,
        total_allocated: report.total_allocated_memsize,
        total_retained: report.total_retained_memsize,
        allocated_objects: report.total_allocated,
        retained_objects: report.total_retained,
        allocated_by_class: report.allocated_memory_by_class.first(5),
        retained_by_class: report.retained_memory_by_class.first(5)
      }
    else
      # Fallback to basic GC stats
      before_stats = GC.stat
      before_memory = get_memory_usage

      yield ->(iteration) {
        # Sample memory at this iteration
        current_stats = GC.stat
        current_memory = get_memory_usage
        elapsed_time = Time.now - start_time

        growth_data << {
          iteration: iteration + 1,
          timestamp: elapsed_time,
          allocated_objects: current_stats[:total_allocated_objects] - before_stats[:total_allocated_objects],
          heap_live_slots: current_stats[:heap_live_slots] - before_stats[:heap_live_slots],
          memory_kb: current_memory - before_memory
        }
      }

      after_stats = GC.stat
      after_memory = get_memory_usage

      memory_result = {
        label: label,
        total_allocated: (after_memory - before_memory) * 1024, # Convert KB to bytes
        total_retained: (after_stats[:heap_live_slots] - before_stats[:heap_live_slots]) * 40, # Estimate
        allocated_objects: after_stats[:total_allocated_objects] - before_stats[:total_allocated_objects],
        retained_objects: after_stats[:heap_live_slots] - before_stats[:heap_live_slots],
        allocated_by_class: [],
        retained_by_class: []
      }
    end

    [memory_result, growth_data]
  end

  def get_memory_usage
    # Try to get memory usage in KB
    if File.exist?('/proc/self/status')
      File.read('/proc/self/status').match(/VmRSS:\s+(\d+)/)[1].to_i rescue 0
    else
      # Fallback for non-Linux systems
      `ps -o rss= -p #{Process.pid}`.to_i rescue 0
    end
  end

  def store_results(category, level, sentry_memory)
    @results[category] ||= {}
    @results[category][level] = {
      sentry: sentry_memory
    }
  end

  def store_growth_data(category, level, growth_data)
    @memory_growth_data[category] ||= {}
    @memory_growth_data[category][level] = growth_data
  end

  def display_sentry_results(test_name, sentry_memory)
    puts "     #{test_name.ljust(20)} | " \
         "Allocated: #{format_memory(sentry_memory[:total_allocated]).rjust(8)} | " \
         "Retained: #{format_memory(sentry_memory[:total_retained]).rjust(8)} | " \
         "Objects: #{sentry_memory[:allocated_objects].to_s.rjust(6)}"
  end

  def generate_memory_graphs
    puts "\n📊 Generating memory growth visualizations..."

    # Ensure tmp directory exists
    Dir.mkdir('tmp') unless Dir.exist?('tmp')

    if GRUFF_AVAILABLE
      generate_svg_charts
    elsif GNUPLOT_AVAILABLE
      generate_gnuplot_charts
    else
      puts "   Generating ASCII graphs (install gruff or gnuplot gems for better visualization)..."
      generate_ascii_graphs
    end
  end

  def generate_svg_charts
    puts "   Creating SVG charts with Gruff..."

    @memory_growth_data.each do |category, levels|
      # Create a combined chart for all levels in this category
      create_category_chart(category, levels)

      # Create individual charts for each level
      levels.each do |level, growth_data|
        next if growth_data.empty?
        create_individual_chart(category, level, growth_data)
      end
    end

    # Create an overview chart with all categories
    create_overview_chart

    puts "   SVG charts saved to tmp/ directory"
  end

  def create_category_chart(category, levels)
    chart = Gruff::Line.new(1200)
    chart.title = "#{category.gsub('_', ' ').split.map(&:capitalize).join(' ')} - Memory Growth Over Time"

    # Set theme and styling
    chart.theme = {
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'],
      marker_color: '#DDDDDD',
      font_color: '#333333',
      background_colors: ['#FFFFFF', '#FFFFFF']
    }

    chart.font = chart.title_font = 'Arial'
    chart.title_font_size = 20
    chart.legend_font_size = 14
    chart.marker_font_size = 12

    # Set up colors for different levels
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    color_index = 0

    levels.each do |level, growth_data|
      next if growth_data.empty?

      # Extract memory values in MB
      memory_values = growth_data.map { |point| point[:memory_kb] / 1024.0 }

      chart.data(level.to_s.upcase, memory_values)
      chart.colors[color_index] = colors[color_index % colors.length]
      color_index += 1
    end

    # Set labels for x-axis (show every few iterations)
    if levels.values.any? { |data| !data.empty? }
      sample_data = levels.values.find { |data| !data.empty? }
      label_step = [sample_data.length / 10, 1].max
      labels = {}
      sample_data.each_with_index do |point, index|
        labels[index] = point[:iteration].to_s if index % label_step == 0
      end
      chart.labels = labels
    end

    chart.y_axis_label = 'Memory Usage (MB)'
    chart.x_axis_label = 'Iterations'
    chart.minimum_value = 0

    filename = "tmp/memory_growth_#{category}.svg"
    chart.write(filename)
    puts "     Created: #{filename}"
  end

  def create_individual_chart(category, level, growth_data)
    chart = Gruff::Line.new(800)
    chart.title = "#{category.gsub('_', ' ').split.map(&:capitalize).join(' ')} - #{level.to_s.upcase} Level"
    chart.font = chart.title_font = 'Arial'
    chart.title_font_size = 18
    chart.marker_font_size = 10

    # Memory usage line
    memory_values = growth_data.map { |point| point[:memory_kb] / 1024.0 }
    chart.data('Memory (MB)', memory_values, '#FF6B6B')

    # Object allocation line (scaled)
    if MEMORY_PROFILER_AVAILABLE
      max_memory = memory_values.max
      object_values = growth_data.map { |point|
        # Scale object count to fit on same chart (rough approximation)
        (point[:allocated_objects] / 1000.0) * (max_memory / memory_values.length)
      }
      chart.data('Objects (K, scaled)', object_values, '#4ECDC4')
    end

    # Set x-axis labels
    label_step = [growth_data.length / 8, 1].max
    labels = {}
    growth_data.each_with_index do |point, index|
      labels[index] = point[:iteration].to_s if index % label_step == 0
    end
    chart.labels = labels

    chart.y_axis_label = 'Memory Usage (MB)'
    chart.x_axis_label = 'Iterations'
    chart.minimum_value = 0

    filename = "tmp/memory_growth_#{category}_#{level}.svg"
    chart.write(filename)
  end

  def create_overview_chart
    chart = Gruff::Line.new(1400)
    chart.title = 'Sentry Memory Usage - All Categories Overview'

    # Set theme and styling
    chart.theme = {
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F39C12', '#E74C3C', '#9B59B6'],
      marker_color: '#DDDDDD',
      font_color: '#333333',
      background_colors: ['#FFFFFF', '#FFFFFF']
    }

    chart.font = chart.title_font = 'Arial'
    chart.title_font_size = 24
    chart.legend_font_size = 12
    chart.marker_font_size = 10

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    color_index = 0

    @memory_growth_data.each do |category, levels|
      levels.each do |level, growth_data|
        next if growth_data.empty?

        memory_values = growth_data.map { |point| point[:memory_kb] / 1024.0 }
        label = "#{category.gsub('_', ' ')}_#{level}"

        chart.data(label, memory_values)
        chart.colors[color_index] = colors[color_index % colors.length]
        color_index += 1
      end
    end

    # Use the first non-empty dataset for x-axis labels
    sample_data = @memory_growth_data.values.flat_map(&:values).find { |data| !data.empty? }
    if sample_data
      label_step = [sample_data.length / 12, 1].max
      labels = {}
      sample_data.each_with_index do |point, index|
        labels[index] = point[:iteration].to_s if index % label_step == 0
      end
      chart.labels = labels
    end

    chart.y_axis_label = 'Memory Usage (MB)'
    chart.x_axis_label = 'Iterations'
    chart.minimum_value = 0

    filename = 'tmp/memory_growth_overview.svg'
    chart.write(filename)
    puts "     Created: #{filename}"
  end

  def generate_gnuplot_charts
    puts "   Creating charts with Gnuplot..."

    @memory_growth_data.each do |category, levels|
      create_gnuplot_chart(category, levels)
    end

    puts "   Gnuplot charts saved to tmp/ directory"
  end

  def create_gnuplot_chart(category, levels)
    Gnuplot.open do |gp|
      Gnuplot::Plot.new(gp) do |plot|
        plot.title "#{category.gsub('_', ' ').split.map(&:capitalize).join(' ')} - Memory Growth Over Time"
        plot.xlabel "Iterations"
        plot.ylabel "Memory Usage (MB)"
        plot.terminal "svg size 1200,800"
        plot.output "tmp/memory_growth_#{category}.svg"
        plot.grid
        plot.key "outside right"

        levels.each_with_index do |(level, growth_data), index|
          next if growth_data.empty?

          iterations = growth_data.map { |point| point[:iteration] }
          memory_mb = growth_data.map { |point| point[:memory_kb] / 1024.0 }

          plot.data << Gnuplot::DataSet.new([iterations, memory_mb]) do |ds|
            ds.with = "linespoints"
            ds.title = level.to_s.upcase
            ds.linewidth = 2
          end
        end
      end
    end

    puts "     Created: tmp/memory_growth_#{category}.svg"
  end

  def generate_ascii_graphs
    @memory_growth_data.each do |category, levels|
      puts "\n#{category.gsub('_', ' ').upcase} - Memory Growth Over Time"
      puts "=" * 60

      levels.each do |level, growth_data|
        next if growth_data.empty?

        puts "\n#{level.upcase} Level:"
        generate_ascii_graph(growth_data, category, level)
      end
    end
  end

  def generate_ascii_graph(growth_data, category, level)
    return if growth_data.empty?

    # Extract memory values (convert to MB for readability)
    memory_values = growth_data.map { |point| point[:memory_kb] / 1024.0 }
    iterations = growth_data.map { |point| point[:iteration] }

    # Graph dimensions
    graph_width = 60
    graph_height = 15

    # Find min/max for scaling
    min_memory = memory_values.min
    max_memory = memory_values.max
    memory_range = max_memory - min_memory

    if memory_range == 0
      puts "     No significant memory growth detected"
      return
    end

    # Create the graph
    puts "     Memory Usage (MB) over #{ITERATIONS} iterations (sampled every #{MEMORY_SAMPLE_INTERVAL}):"
    puts "     Max: #{sprintf('%.2f', max_memory)} MB"

    # Draw the graph from top to bottom
    (0...graph_height).each do |row|
      line = "     "
      threshold = max_memory - (row.to_f / (graph_height - 1)) * memory_range

      (0...graph_width).each do |col|
        data_index = (col.to_f / (graph_width - 1) * (memory_values.length - 1)).round
        value = memory_values[data_index]

        if (value - threshold).abs < (memory_range / graph_height / 2)
          line += "█"
        elsif value > threshold
          line += "▄"
        else
          line += " "
        end
      end

      # Add y-axis labels
      line += sprintf(" %.2f", threshold)
      puts line
    end

    # X-axis
    puts "     " + "─" * graph_width
    puts "     #{iterations.first}#{' ' * (graph_width - iterations.last.to_s.length - iterations.first.to_s.length)}#{iterations.last} (iterations)"

    # Show memory growth rate
    if growth_data.length > 1
      initial_memory = growth_data.first[:memory_kb] / 1024.0
      final_memory = growth_data.last[:memory_kb] / 1024.0
      growth_rate = ((final_memory - initial_memory) / initial_memory * 100) rescue 0
      puts "     Growth rate: #{sprintf('%.1f%%', growth_rate)} (#{sprintf('%.2f', initial_memory)} → #{sprintf('%.2f', final_memory)} MB)"
    end
  end

  def display_summary
    puts "\n📈 Sentry Memory Usage Summary"
    puts "=" * 70

    total_allocated = 0
    total_retained = 0
    total_objects = 0

    @results.each do |category, levels|
      puts "\n#{category.gsub('_', ' ').upcase}:"
      category_allocated = 0
      category_retained = 0

      levels.each do |level, data|
        sentry_data = data[:sentry]
        total_allocated += sentry_data[:total_allocated]
        total_retained += sentry_data[:total_retained]
        total_objects += sentry_data[:allocated_objects]
        category_allocated += sentry_data[:total_allocated]
        category_retained += sentry_data[:total_retained]

        puts "  #{level}: #{format_memory(sentry_data[:total_allocated])} allocated, " \
             "#{format_memory(sentry_data[:total_retained])} retained, " \
             "#{sentry_data[:allocated_objects]} objects"
      end

      puts "  Category total: #{format_memory(category_allocated)} allocated, #{format_memory(category_retained)} retained"
    end

    puts "\nOVERALL SENTRY MEMORY USAGE:"
    puts "  Total allocated: #{format_memory(total_allocated)}"
    puts "  Total retained:  #{format_memory(total_retained)}"
    puts "  Total objects:   #{total_objects}"
    puts "  Average per iteration: #{format_memory(total_allocated / ITERATIONS)}"

    # Memory efficiency analysis
    retention_rate = total_retained.to_f / total_allocated * 100
    puts "  Memory retention rate: #{sprintf('%.1f%%', retention_rate)}"

    if total_objects > 0
      avg_object_size = total_allocated.to_f / total_objects
      puts "  Average object size: #{sprintf('%.1f', avg_object_size)} bytes"
    end

    # Identify memory hotspots
    puts "\nMEMORY HOTSPOTS:"
    hotspots = []
    @results.each do |category, levels|
      levels.each do |level, data|
        hotspots << {
          name: "#{category}_#{level}",
          allocated: data[:sentry][:total_allocated],
          retained: data[:sentry][:total_retained]
        }
      end
    end

    hotspots.sort_by { |h| h[:allocated] }.reverse.first(5).each_with_index do |hotspot, index|
      percentage = hotspot[:allocated].to_f / total_allocated * 100
      puts "  #{index + 1}. #{hotspot[:name]}: #{format_memory(hotspot[:allocated])} (#{sprintf('%.1f%%', percentage)})"
    end
  end

  def generate_report
    puts "\n💾 Generating detailed report..."

    report_data = {
      timestamp: Time.now.iso8601,
      ruby_version: RUBY_VERSION,
      sentry_version: Sentry::VERSION,
      baseline_memory: @baseline_memory,
      test_configuration: {
        iterations: ITERATIONS,
        log_levels: LOG_LEVELS,
        memory_sample_interval: MEMORY_SAMPLE_INTERVAL,
        visualization_gems: {
          gruff: GRUFF_AVAILABLE,
          gnuplot: GNUPLOT_AVAILABLE,
          memory_profiler: MEMORY_PROFILER_AVAILABLE
        }
      },
      results: @results,
      memory_growth_data: @memory_growth_data
    }

    # Ensure tmp directory exists
    Dir.mkdir('tmp') unless Dir.exist?('tmp')

    File.write('tmp/sentry_memory_analysis.json', JSON.pretty_generate(report_data))
    puts "   Report saved to: tmp/sentry_memory_analysis.json"

    # Also generate a CSV for easy analysis
    generate_csv_report

    # Generate HTML site with visualizations
    generate_html_site

    # List generated visualization files
    list_generated_files
  end

  def list_generated_files
    puts "\n📁 Generated files:"
    puts "   📊 Data files:"
    puts "     - tmp/sentry_memory_analysis.json (detailed analysis)"
    puts "     - tmp/sentry_memory_growth.csv (growth data)"

    puts "   🌐 HTML Report:"
    puts "     - tmp/sentry_memory_analysis.html (interactive web report)"
    puts "     - tmp/sentry_memory_analysis.css (stylesheet)"

    if GRUFF_AVAILABLE || GNUPLOT_AVAILABLE
      puts "   📈 Visualization files:"
      Dir.glob('tmp/memory_growth_*.svg').sort.each do |file|
        puts "     - #{file}"
      end

      puts "\n   🚀 Quick Start:"
      puts "     Open tmp/sentry_memory_analysis.html in your browser for the complete report!"
    else
      puts "\n   💡 Install visualization gems for enhanced HTML report:"
      puts "     gem install gruff  # Requires ImageMagick"
      puts "     gem install gnuplot  # Requires gnuplot system package"
      puts "\n   🚀 Quick Start:"
      puts "     Open tmp/sentry_memory_analysis.html in your browser for the analysis report!"
    end
  end

  def generate_csv_report
    require 'csv'

    CSV.open('tmp/sentry_memory_growth.csv', 'w') do |csv|
      csv << ['Category', 'Level', 'Iteration', 'Timestamp', 'Memory_KB', 'Allocated_Objects', 'Heap_Live_Slots']

      @memory_growth_data.each do |category, levels|
        levels.each do |level, growth_data|
          growth_data.each do |point|
            csv << [
              category,
              level,
              point[:iteration],
              point[:timestamp],
              point[:memory_kb],
              point[:allocated_objects],
              point[:heap_live_slots]
            ]
          end
        end
      end
    end

    puts "   Growth data CSV saved to: tmp/sentry_memory_growth.csv"
  end

  def generate_html_site
    puts "   Generating HTML site..."

    html_content = generate_html_content
    File.write('tmp/sentry_memory_analysis.html', html_content)

    # Generate CSS file
    css_content = generate_css_content
    File.write('tmp/sentry_memory_analysis.css', css_content)

    puts "   HTML site saved to: tmp/sentry_memory_analysis.html"
  end

  def generate_html_content
    # Calculate summary statistics
    total_allocated = @results.values.flat_map(&:values).sum { |data| data[:sentry][:total_allocated] }
    total_retained = @results.values.flat_map(&:values).sum { |data| data[:sentry][:total_retained] }
    total_objects = @results.values.flat_map(&:values).sum { |data| data[:sentry][:allocated_objects] }
    retention_rate = total_retained.to_f / total_allocated * 100

    # Get hotspots
    hotspots = []
    @results.each do |category, levels|
      levels.each do |level, data|
        hotspots << {
          name: "#{category.gsub('_', ' ').split.map(&:capitalize).join(' ')} - #{level.to_s.upcase}",
          allocated: data[:sentry][:total_allocated],
          percentage: data[:sentry][:total_allocated].to_f / total_allocated * 100
        }
      end
    end
    hotspots.sort_by! { |h| h[:allocated] }.reverse!

    <<~HTML
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Sentry Memory Usage Analysis</title>
          <link rel="stylesheet" href="sentry_memory_analysis.css">
      </head>
      <body>
          <div class="container">
              <header>
                  <h1>🔍 Sentry Memory Usage Analysis</h1>
                  <div class="meta-info">
                      <span>Generated: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}</span>
                      <span>Ruby: #{RUBY_VERSION}</span>
                      <span>Sentry: #{Sentry::VERSION}</span>
                      <span>Iterations: #{ITERATIONS}</span>
                  </div>
              </header>

              <section class="summary">
                  <h2>📊 Executive Summary</h2>
                  <div class="summary-grid">
                      <div class="summary-card">
                          <h3>Total Memory Allocated</h3>
                          <div class="metric">#{format_memory(total_allocated)}</div>
                          <p>Total memory allocated during all logging operations</p>
                      </div>
                      <div class="summary-card">
                          <h3>Memory Retained</h3>
                          <div class="metric">#{format_memory(total_retained)}</div>
                          <p>Memory that remains after garbage collection</p>
                      </div>
                      <div class="summary-card">
                          <h3>Objects Created</h3>
                          <div class="metric">#{total_objects.to_s.gsub(/\\B(?=(...)*\\z)/, ',')}</div>
                          <p>Total Ruby objects allocated during tests</p>
                      </div>
                      <div class="summary-card">
                          <h3>Retention Rate</h3>
                          <div class="metric">#{sprintf('%.1f%%', retention_rate)}</div>
                          <p>Percentage of memory retained after GC</p>
                      </div>
                  </div>
              </section>

              <section class="insights">
                  <h2>🎯 Key Insights</h2>
                  <div class="insights-grid">
                      <div class="insight-card">
                          <h3>💡 Memory Efficiency</h3>
                          <p>Sentry logging retains <strong>#{sprintf('%.1f%%', retention_rate)}</strong> of allocated memory after garbage collection, indicating efficient memory management with some necessary buffering for log event processing.</p>
                      </div>
                      <div class="insight-card">
                          <h3>📈 Object Allocation</h3>
                          <p>Each log entry creates approximately <strong>#{(total_objects.to_f / ITERATIONS / @results.values.flat_map(&:keys).length).round}</strong> Ruby objects on average, reflecting Sentry's rich object model for structured logging.</p>
                      </div>
                      <div class="insight-card">
                          <h3>🏆 Performance Leader</h3>
                          <p><strong>#{hotspots.last[:name]}</strong> uses the least memory (#{format_memory(hotspots.last[:allocated])}), while <strong>#{hotspots.first[:name]}</strong> uses the most (#{format_memory(hotspots.first[:allocated])}).</p>
                      </div>
                      <div class="insight-card">
                          <h3>⚡ Optimization Opportunity</h3>
                          <p>Template messages show highest memory usage due to parameter processing. Consider caching compiled templates for frequently used patterns.</p>
                      </div>
                  </div>
              </section>

              #{generate_charts_section}

              <section class="detailed-results">
                  <h2>📋 Detailed Results</h2>
                  #{generate_results_tables}
              </section>

              <section class="memory-hotspots">
                  <h2>🔥 Memory Hotspots</h2>
                  <div class="hotspots-table">
                      <table>
                          <thead>
                              <tr>
                                  <th>Rank</th>
                                  <th>Scenario</th>
                                  <th>Memory Allocated</th>
                                  <th>Percentage</th>
                                  <th>Impact</th>
                              </tr>
                          </thead>
                          <tbody>
                              #{hotspots.first(10).map.with_index do |hotspot, index|
                                impact_class = case index
                                when 0..2 then 'high-impact'
                                when 3..5 then 'medium-impact'
                                else 'low-impact'
                                end

                                "<tr class='#{impact_class}'>
                                    <td>#{index + 1}</td>
                                    <td>#{hotspot[:name]}</td>
                                    <td>#{format_memory(hotspot[:allocated])}</td>
                                    <td>#{sprintf('%.1f%%', hotspot[:percentage])}</td>
                                    <td><span class='impact-badge #{impact_class}'>#{impact_class.gsub('-', ' ').upcase}</span></td>
                                </tr>"
                              end.join}
                          </tbody>
                      </table>
                  </div>
              </section>

              <section class="recommendations">
                  <h2>💡 Recommendations</h2>
                  <div class="recommendations-grid">
                      <div class="recommendation-card">
                          <h3>🎯 For High-Volume Logging</h3>
                          <ul>
                              <li>Use simple string messages for debug/trace logging</li>
                              <li>Reserve structured data for important events</li>
                              <li>Implement log level controls in production</li>
                              <li>Consider async logging for performance-critical paths</li>
                          </ul>
                      </div>
                      <div class="recommendation-card">
                          <h3>🔧 For Memory Optimization</h3>
                          <ul>
                              <li>Cache compiled templates for repeated patterns</li>
                              <li>Limit structured data complexity</li>
                              <li>Use log sampling for high-frequency events</li>
                              <li>Monitor memory usage in production</li>
                          </ul>
                      </div>
                      <div class="recommendation-card">
                          <h3>📊 For Monitoring</h3>
                          <ul>
                              <li>Set up memory usage alerts</li>
                              <li>Track log volume metrics</li>
                              <li>Monitor GC frequency and duration</li>
                              <li>Use this tool for regression testing</li>
                          </ul>
                      </div>
                  </div>
              </section>

              <footer>
                  <p>Generated by Sentry Memory Analyzer • <a href="sentry_memory_analysis.json">Download JSON Report</a> • <a href="sentry_memory_growth.csv">Download CSV Data</a></p>
              </footer>
          </div>
      </body>
      </html>
    HTML
  end

  def generate_charts_section
    if GRUFF_AVAILABLE || GNUPLOT_AVAILABLE
      chart_files = Dir.glob('tmp/memory_growth_*.svg').sort

      charts_html = <<~HTML
        <section class="charts">
            <h2>📈 Memory Growth Visualizations</h2>
            <p class="charts-description">
                These charts show how memory usage grows over time during logging operations.
                Each chart represents #{ITERATIONS} iterations with memory sampled every #{MEMORY_SAMPLE_INTERVAL} operations.
            </p>

            <div class="chart-grid">
      HTML

      # Add overview chart first if it exists
      overview_chart = chart_files.find { |f| f.include?('overview') }
      if overview_chart
        charts_html += <<~HTML
          <div class="chart-container featured">
              <h3>🌟 Complete Overview</h3>
              <p>Combined view of all logging scenarios showing relative memory usage patterns.</p>
              <div class="chart-wrapper">
                  <img src="#{File.basename(overview_chart)}" alt="Memory Growth Overview" class="chart-svg" />
                  <p class="chart-fallback">If chart doesn't display, <a href="#{File.basename(overview_chart)}" target="_blank">open SVG directly</a></p>
              </div>
          </div>
        HTML
      end

      # Add category charts
      @results.each do |category, levels|
        category_chart = chart_files.find { |f| f.include?(category) && !f.include?('_debug') && !f.include?('_info') && !f.include?('_warn') && !f.include?('_error') }
        next unless category_chart

        category_title = category.gsub('_', ' ').split.map(&:capitalize).join(' ')
        category_description = get_category_description(category)

        charts_html += <<~HTML
          <div class="chart-container">
              <h3>#{get_category_emoji(category)} #{category_title}</h3>
              <p>#{category_description}</p>
              <div class="chart-wrapper">
                  <img src="#{File.basename(category_chart)}" alt="#{category_title} Memory Growth" class="chart-svg" />
                  <p class="chart-fallback">If chart doesn't display, <a href="#{File.basename(category_chart)}" target="_blank">open SVG directly</a></p>
              </div>
          </div>
        HTML
      end

      charts_html += <<~HTML
            </div>
        </section>
      HTML
    else
      # Generate simple HTML charts as fallback
      fallback_charts = generate_fallback_charts
      <<~HTML
        <section class="charts">
            <h2>📈 Memory Growth Visualizations</h2>
            <div class="charts-description">
                <p>📊 Simple bar charts showing memory usage comparison. Install visualization gems for advanced charts:</p>
                <pre><code>gem install gruff  # Requires ImageMagick
gem install gnuplot  # Requires gnuplot</code></pre>
            </div>
            #{fallback_charts}
        </section>
      HTML
    end
  end

  def generate_fallback_charts
    # Create simple HTML/CSS bar charts as fallback
    charts_html = '<div class="fallback-charts">'

    @results.each do |category, levels|
      category_title = category.gsub('_', ' ').split.map(&:capitalize).join(' ')

      charts_html += <<~HTML
        <div class="fallback-chart">
            <h3>#{get_category_emoji(category)} #{category_title}</h3>
            <div class="bar-chart">
      HTML

      # Calculate max value for scaling
      max_value = levels.values.map { |data| data[:sentry][:total_allocated] }.max

      levels.each do |level, data|
        allocated = data[:sentry][:total_allocated]
        percentage = (allocated.to_f / max_value * 100).round(1)

        charts_html += <<~HTML
          <div class="bar-item">
              <div class="bar-label">#{level.to_s.upcase}</div>
              <div class="bar-container">
                  <div class="bar bar-#{level}" style="width: #{percentage}%"></div>
                  <span class="bar-value">#{format_memory(allocated)}</span>
              </div>
          </div>
        HTML
      end

      charts_html += <<~HTML
            </div>
        </div>
      HTML
    end

    charts_html += '</div>'
    charts_html
  end

  def get_category_emoji(category)
    case category
    when 'simple_strings' then '🔤'
    when 'structured_data' then '🏗️'
    when 'exceptions' then '💥'
    when 'template_messages' then '📝'
    else '📊'
    end
  end

  def get_category_description(category)
    case category
    when 'simple_strings'
      'Basic log messages with string content only. Shows baseline memory usage for standard logging operations.'
    when 'structured_data'
      'Log messages with hash attributes and metadata. Demonstrates memory overhead of structured logging features.'
    when 'exceptions'
      'Error logging with exception details and backtraces. Shows memory impact of error handling and context capture.'
    when 'template_messages'
      'Parameterized log messages with variable substitution. Highest memory usage due to template processing overhead.'
    else
      'Memory usage analysis for this logging scenario.'
    end
  end

  def generate_results_tables
    tables_html = ""

    @results.each do |category, levels|
      category_title = category.gsub('_', ' ').split.map(&:capitalize).join(' ')
      category_total_allocated = levels.values.sum { |data| data[:sentry][:total_allocated] }
      category_total_retained = levels.values.sum { |data| data[:sentry][:total_retained] }

      tables_html += <<~HTML
        <div class="results-table">
            <h3>#{get_category_emoji(category)} #{category_title}</h3>
            <table>
                <thead>
                    <tr>
                        <th>Log Level</th>
                        <th>Memory Allocated</th>
                        <th>Memory Retained</th>
                        <th>Objects Created</th>
                        <th>Retention Rate</th>
                        <th>Avg per Iteration</th>
                    </tr>
                </thead>
                <tbody>
                    #{levels.map do |level, data|
                      sentry_data = data[:sentry]
                      retention_rate = sentry_data[:total_retained].to_f / sentry_data[:total_allocated] * 100
                      avg_per_iteration = sentry_data[:total_allocated] / ITERATIONS

                      "<tr>
                          <td><span class='log-level #{level}'>#{level.to_s.upcase}</span></td>
                          <td>#{format_memory(sentry_data[:total_allocated])}</td>
                          <td>#{format_memory(sentry_data[:total_retained])}</td>
                          <td>#{sentry_data[:allocated_objects].to_s.gsub(/\\B(?=(...)*\\z)/, ',')}</td>
                          <td>#{sprintf('%.1f%%', retention_rate)}</td>
                          <td>#{format_memory(avg_per_iteration)}</td>
                      </tr>"
                    end.join}
                </tbody>
                <tfoot>
                    <tr class="category-total">
                        <td><strong>Category Total</strong></td>
                        <td><strong>#{format_memory(category_total_allocated)}</strong></td>
                        <td><strong>#{format_memory(category_total_retained)}</strong></td>
                        <td><strong>#{levels.values.sum { |data| data[:sentry][:allocated_objects] }.to_s.gsub(/\\B(?=(...)*\\z)/, ',')}</strong></td>
                        <td><strong>#{sprintf('%.1f%%', category_total_retained.to_f / category_total_allocated * 100)}</strong></td>
                        <td><strong>#{format_memory(category_total_allocated / ITERATIONS / levels.length)}</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
      HTML
    end

    tables_html
  end

  def generate_css_content
    <<~CSS
      /* Sentry Memory Analysis Styles */
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }

      body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
      }

      .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          background: white;
          margin-top: 20px;
          margin-bottom: 20px;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      }

      header {
          text-align: center;
          margin-bottom: 40px;
          padding-bottom: 20px;
          border-bottom: 3px solid #667eea;
      }

      header h1 {
          font-size: 2.5em;
          color: #2c3e50;
          margin-bottom: 10px;
      }

      .meta-info {
          display: flex;
          justify-content: center;
          gap: 20px;
          flex-wrap: wrap;
          color: #666;
          font-size: 0.9em;
      }

      .meta-info span {
          background: #f8f9fa;
          padding: 5px 12px;
          border-radius: 20px;
          border: 1px solid #e9ecef;
      }

      section {
          margin-bottom: 40px;
      }

      h2 {
          font-size: 1.8em;
          color: #2c3e50;
          margin-bottom: 20px;
          border-left: 4px solid #667eea;
          padding-left: 15px;
      }

      h3 {
          font-size: 1.3em;
          color: #34495e;
          margin-bottom: 15px;
      }

      /* Summary Grid */
      .summary-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
      }

      .summary-card {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 25px;
          border-radius: 12px;
          text-align: center;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }

      .summary-card h3 {
          color: white;
          font-size: 1.1em;
          margin-bottom: 10px;
      }

      .summary-card .metric {
          font-size: 2.2em;
          font-weight: bold;
          margin: 10px 0;
      }

      .summary-card p {
          font-size: 0.9em;
          opacity: 0.9;
      }

      /* Insights Grid */
      .insights-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 20px;
      }

      .insight-card {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #28a745;
      }

      .insight-card h3 {
          color: #28a745;
          margin-bottom: 10px;
      }

      /* Charts Section */
      .charts-description {
          background: #e3f2fd;
          padding: 15px;
          border-radius: 8px;
          margin-bottom: 25px;
          border-left: 4px solid #2196f3;
      }

      .chart-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 30px;
      }

      .chart-container {
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      }

      .chart-container.featured {
          border: 2px solid #667eea;
          background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
      }

      .chart-wrapper {
          margin-top: 15px;
          text-align: center;
      }

      .chart-svg {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          border: 1px solid #e9ecef;
      }

      .chart-fallback {
          margin-top: 10px;
          font-size: 0.9em;
          color: #666;
      }

      .chart-fallback a {
          color: #667eea;
          text-decoration: none;
      }

      .chart-fallback a:hover {
          text-decoration: underline;
      }

      .no-charts-message {
          text-align: center;
          padding: 40px;
          background: #fff3cd;
          border-radius: 8px;
          border: 1px solid #ffeaa7;
      }

      .no-charts-message pre {
          background: #2c3e50;
          color: #ecf0f1;
          padding: 15px;
          border-radius: 4px;
          margin: 15px 0;
          display: inline-block;
      }

      /* Fallback Charts */
      .fallback-charts {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: 30px;
          margin-top: 20px;
      }

      .fallback-chart {
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      }

      .fallback-chart h3 {
          margin-bottom: 20px;
          color: #2c3e50;
      }

      .bar-chart {
          display: flex;
          flex-direction: column;
          gap: 15px;
      }

      .bar-item {
          display: flex;
          align-items: center;
          gap: 15px;
      }

      .bar-label {
          min-width: 60px;
          font-weight: bold;
          font-size: 0.9em;
      }

      .bar-container {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 10px;
      }

      .bar {
          height: 25px;
          border-radius: 4px;
          min-width: 20px;
          transition: all 0.3s ease;
      }

      .bar:hover {
          opacity: 0.8;
      }

      .bar-debug { background: #6c757d; }
      .bar-info { background: #17a2b8; }
      .bar-warn { background: #ffc107; }
      .bar-error { background: #dc3545; }

      .bar-value {
          font-size: 0.9em;
          color: #666;
          min-width: 60px;
      }

      /* Tables */
      .results-table {
          margin-bottom: 30px;
      }

      table {
          width: 100%;
          border-collapse: collapse;
          background: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      }

      th, td {
          padding: 12px 15px;
          text-align: left;
          border-bottom: 1px solid #e9ecef;
      }

      th {
          background: #667eea;
          color: white;
          font-weight: 600;
      }

      tbody tr:hover {
          background: #f8f9fa;
      }

      .category-total {
          background: #e3f2fd !important;
          font-weight: 600;
      }

      .log-level {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.8em;
          font-weight: bold;
      }

      .log-level.debug { background: #6c757d; color: white; }
      .log-level.info { background: #17a2b8; color: white; }
      .log-level.warn { background: #ffc107; color: #212529; }
      .log-level.error { background: #dc3545; color: white; }

      /* Hotspots */
      .hotspots-table table {
          margin-top: 20px;
      }

      .impact-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.7em;
          font-weight: bold;
      }

      .high-impact .impact-badge.high-impact { background: #dc3545; color: white; }
      .medium-impact .impact-badge.medium-impact { background: #ffc107; color: #212529; }
      .low-impact .impact-badge.low-impact { background: #28a745; color: white; }

      /* Recommendations */
      .recommendations-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
      }

      .recommendation-card {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #17a2b8;
      }

      .recommendation-card h3 {
          color: #17a2b8;
          margin-bottom: 15px;
      }

      .recommendation-card ul {
          list-style: none;
          padding-left: 0;
      }

      .recommendation-card li {
          padding: 5px 0;
          padding-left: 20px;
          position: relative;
      }

      .recommendation-card li:before {
          content: "✓";
          position: absolute;
          left: 0;
          color: #28a745;
          font-weight: bold;
      }

      /* Footer */
      footer {
          text-align: center;
          padding: 20px;
          border-top: 1px solid #e9ecef;
          color: #666;
          margin-top: 40px;
      }

      footer a {
          color: #667eea;
          text-decoration: none;
          margin: 0 10px;
      }

      footer a:hover {
          text-decoration: underline;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
          .container {
              margin: 10px;
              padding: 15px;
          }

          header h1 {
              font-size: 2em;
          }

          .meta-info {
              flex-direction: column;
              align-items: center;
          }

          .summary-grid,
          .insights-grid,
          .recommendations-grid {
              grid-template-columns: 1fr;
          }

          table {
              font-size: 0.9em;
          }

          th, td {
              padding: 8px 10px;
          }
      }
    CSS
  end

  def format_memory(bytes)
    return "0 B" if bytes.nil? || bytes == 0

    units = ['B', 'KB', 'MB', 'GB']
    size = bytes.to_f
    unit_index = 0

    while size >= 1024 && unit_index < units.length - 1
      size /= 1024
      unit_index += 1
    end

    if size >= 100
      "#{size.round(0).to_i} #{units[unit_index]}"
    elsif size >= 10
      "#{sprintf('%.1f', size)} #{units[unit_index]}"
    else
      "#{sprintf('%.2f', size)} #{units[unit_index]}"
    end
  end

  def format_memory_diff(bytes)
    sign = bytes >= 0 ? "+" : ""
    "#{sign}#{format_memory(bytes.abs)}"
  end
end

# Note: Using Sentry::DummyTransport to prevent actual network calls

# Run the analysis if this file is executed directly
if __FILE__ == $0
  analyzer = SentryMemoryAnalyzer.new
  analyzer.run
end
