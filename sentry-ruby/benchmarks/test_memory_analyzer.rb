#!/usr/bin/env ruby
# frozen_string_literal: true

# Simple test to verify the memory analyzer works
puts "Testing memory analyzer..."

# Add the lib directory to the load path
$LOAD_PATH.unshift(File.expand_path('../lib', __dir__))

begin
  require 'sentry-ruby'
  puts "✓ Sentry loaded successfully"
rescue => e
  puts "✗ Failed to load Sentry: #{e.message}"
  exit 1
end

# Test basic Sentry initialization
begin
  Sentry.init do |config|
    config.dsn = "https://<EMAIL>/dummy"
    config.enable_logs = true
    config.sdk_logger = Logger.new(File::NULL)
    config.background_worker_threads = 0
    config.transport.transport_class = Sentry::DummyTransport
  end
  puts "✓ Sentry initialized successfully"
rescue => e
  puts "✗ Failed to initialize Sentry: #{e.message}"
  exit 1
end

# Test basic logging
begin
  Sentry.logger.info("Test message")
  puts "✓ Sentry logging works"
rescue => e
  puts "✗ Sentry logging failed: #{e.message}"
  exit 1
end

# Test memory profiler availability
begin
  require 'memory_profiler'
  puts "✓ memory_profiler gem available"
rescue LoadError
  puts "⚠ memory_profiler gem not available"
end

# Test gruff availability
begin
  require 'gruff'
  puts "✓ gruff gem available for SVG charts"
rescue LoadError
  puts "⚠ gruff gem not available (install with: gem install gruff)"
end

# Test gnuplot availability
begin
  require 'gnuplot'
  puts "✓ gnuplot gem available"
rescue LoadError
  puts "⚠ gnuplot gem not available (install with: gem install gnuplot)"
end

puts "\nAll basic tests passed! The memory analyzer should work correctly."
puts "Run: ruby benchmarks/memory_usage_comparison.rb"
