#!/usr/bin/env ruby
# frozen_string_literal: true

# Simple test to verify HTML generation works correctly
puts "Testing HTML generation..."

# Add the lib directory to the load path
$LOAD_PATH.unshift(File.expand_path('../lib', __dir__))

require 'sentry-ruby'

# Test HTML generation with minimal data
class TestHTMLGeneration
  def initialize
    @results = {
      'simple_strings' => {
        info: {
          sentry: {
            total_allocated: 167000,
            total_retained: 52500,
            allocated_objects: 1566
          }
        },
        error: {
          sentry: {
            total_allocated: 151000,
            total_retained: 40600,
            allocated_objects: 1499
          }
        }
      }
    }
    @memory_growth_data = {}
  end

  def test_html_generation
    puts "✓ Testing HTML content generation..."
    
    # Test basic HTML structure
    html_content = generate_test_html
    
    # Check for required elements
    checks = [
      html_content.include?('<!DOCTYPE html>'),
      html_content.include?('<title>Sentry Memory Usage Analysis</title>'),
      html_content.include?('Executive Summary'),
      html_content.include?('Memory Hotspots'),
      html_content.include?('log-level info'),
      html_content.include?('log-level error'),
      html_content.include?('167 KB'),
      html_content.include?('151 KB')
    ]
    
    if checks.all?
      puts "✓ HTML content structure is correct"
    else
      puts "✗ HTML content structure has issues"
      return false
    end
    
    # Test CSS generation
    css_content = generate_test_css
    css_checks = [
      css_content.include?('body {'),
      css_content.include?('.summary-card'),
      css_content.include?('.log-level'),
      css_content.include?('background:'),
      css_content.include?('color:')
    ]
    
    if css_checks.all?
      puts "✓ CSS content structure is correct"
    else
      puts "✗ CSS content structure has issues"
      return false
    end
    
    # Test file writing
    Dir.mkdir('tmp') unless Dir.exist?('tmp')
    File.write('tmp/test_report.html', html_content)
    File.write('tmp/test_report.css', css_content)
    
    if File.exist?('tmp/test_report.html') && File.exist?('tmp/test_report.css')
      puts "✓ HTML and CSS files written successfully"
      puts "   Test files: tmp/test_report.html, tmp/test_report.css"
    else
      puts "✗ Failed to write HTML/CSS files"
      return false
    end
    
    true
  end

  private

  def generate_test_html
    total_allocated = 318000
    total_retained = 93100
    total_objects = 3065
    retention_rate = 29.2

    <<~HTML
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Sentry Memory Usage Analysis</title>
          <link rel="stylesheet" href="test_report.css">
      </head>
      <body>
          <div class="container">
              <header>
                  <h1>🔍 Sentry Memory Usage Analysis</h1>
                  <div class="meta-info">
                      <span>Generated: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}</span>
                      <span>Ruby: #{RUBY_VERSION}</span>
                      <span>Test Mode</span>
                  </div>
              </header>

              <section class="summary">
                  <h2>📊 Executive Summary</h2>
                  <div class="summary-grid">
                      <div class="summary-card">
                          <h3>Total Memory Allocated</h3>
                          <div class="metric">318 KB</div>
                          <p>Total memory allocated during all logging operations</p>
                      </div>
                      <div class="summary-card">
                          <h3>Memory Retained</h3>
                          <div class="metric">93.1 KB</div>
                          <p>Memory that remains after garbage collection</p>
                      </div>
                  </div>
              </section>

              <section class="detailed-results">
                  <h2>📋 Test Results</h2>
                  <div class="results-table">
                      <h3>🔤 Simple Strings</h3>
                      <table>
                          <thead>
                              <tr>
                                  <th>Log Level</th>
                                  <th>Memory Allocated</th>
                                  <th>Memory Retained</th>
                                  <th>Objects Created</th>
                              </tr>
                          </thead>
                          <tbody>
                              <tr>
                                  <td><span class='log-level info'>INFO</span></td>
                                  <td>167 KB</td>
                                  <td>52.5 KB</td>
                                  <td>1566</td>
                              </tr>
                              <tr>
                                  <td><span class='log-level error'>ERROR</span></td>
                                  <td>151 KB</td>
                                  <td>40.6 KB</td>
                                  <td>1499</td>
                              </tr>
                          </tbody>
                      </table>
                  </div>
              </section>

              <section class="memory-hotspots">
                  <h2>🔥 Memory Hotspots</h2>
                  <p>This is a test of the HTML generation functionality.</p>
              </section>

              <footer>
                  <p>Generated by Sentry Memory Analyzer Test</p>
              </footer>
          </div>
      </body>
      </html>
    HTML
  end

  def generate_test_css
    <<~CSS
      body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
      }

      .container {
          max-width: 1200px;
          margin: 20px auto;
          padding: 20px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      }

      .summary-card {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 25px;
          border-radius: 12px;
          text-align: center;
          margin: 10px;
      }

      .log-level {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.8em;
          font-weight: bold;
      }

      .log-level.info { background: #17a2b8; color: white; }
      .log-level.error { background: #dc3545; color: white; }

      table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
      }

      th, td {
          padding: 12px;
          text-align: left;
          border-bottom: 1px solid #ddd;
      }

      th {
          background: #667eea;
          color: white;
      }
    CSS
  end
end

# Run the test
if __FILE__ == $0
  tester = TestHTMLGeneration.new
  if tester.test_html_generation
    puts "\n🎉 All HTML generation tests passed!"
    puts "   You can open tmp/test_report.html to verify the output"
  else
    puts "\n❌ HTML generation tests failed!"
    exit 1
  end
end
